/**
 * 高亮管理器 - 负责文本高亮功能
 * 从 content.ts 中提取的高亮相关逻辑
 */

import { LucidHighlight } from '../features/highlight';
// debugContent 导入已移除，使用手动 console.log

export interface HighlightManagerOptions {
  effects?: string[];
  enabled?: boolean;
  minWordLength?: number;
  maxWordLength?: number;
}

export class HighlightManager {
  private highlighter: LucidHighlight | null = null;
  private options: HighlightManagerOptions;

  constructor(options: HighlightManagerOptions = {}) {
    this.options = {
      effects: ['lu-gradient', 'lu-underline'],
      enabled: true,
      minWordLength: 2,
      maxWordLength: 30,
      ...options
    };
  }

  /**
   * 初始化高亮系统
   */
  async initialize(): Promise<void> {
    try {
      console.log('✅ [highlight-manager|STARTUP] 高亮系统初始化中...');
      
      this.highlighter = new LucidHighlight(this.options);
      await this.highlighter.initialize();

      // Store globally for console debugging
      (window as any).lucidHighlighter = this.highlighter;
      
      console.log('✅ [highlight-manager|STARTUP] Highlight system initialized successfully!');
    } catch (error) {
      console.log('❌ [highlight-manager|ERROR] Error initializing highlight system:', error);
      throw error;
    }
  }

  /**
   * 获取高亮器实例
   */
  getHighlighter(): LucidHighlight | null {
    return this.highlighter;
  }

  /**
   * 获取高亮统计信息
   */
  getStats(): any {
    if (!this.highlighter) {
      return { error: 'Highlighter not initialized' };
    }
    return {
      stats: this.highlighter.getHighlightStats(),
      words: this.highlighter.getHighlightedWords()
    };
  }

  /**
   * 清除所有高亮
   */
  async clearAll(): Promise<void> {
    if (!this.highlighter) {
      console.log('⚠️ [highlight-manager|WARN] Highlighter not initialized');
      return;
    }
    
    await this.highlighter.clearAll();
    console.log('✅ [highlight-manager|INFO] All highlights cleared');
  }

  /**
   * 销毁高亮器
   */
  destroy(): void {
    if (this.highlighter) {
      this.highlighter.destroy();
      this.highlighter = null;
    }
  }
}

/**
 * 初始化高亮管理器的便捷函数
 */
export async function initializeHighlighting(options?: HighlightManagerOptions): Promise<HighlightManager> {
  const manager = new HighlightManager(options);
  await manager.initialize();
  return manager;
}