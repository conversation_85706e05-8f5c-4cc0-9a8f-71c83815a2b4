/**
 * Tooltip 管理器 - 负责智能Tooltip交互系统
 * 从 content.ts 中提取的 tooltip 相关逻辑
 */

import React from 'react';
import { uiManager } from '../ui-manager';
import { DynamicTooltip } from '../components/DynamicTooltip/DynamicTooltip';
import { errorLogger, ErrorContext } from '../utils/error-logger';
import { TOOLTIP_CONFIG } from '../constants/slider-config';

export interface TooltipManagerOptions {
  showDelay?: number;
  hideDelay?: number;
  graceMargin?: number;
  mouseThrottle?: number;
  theme?: 'dark' | 'light';
}

export class TooltipManager {
  private options: Required<TooltipManagerOptions>;
  
  // 状态管理
  private showTimer: number | null = null;
  private hideTimer: number | null = null;
  private currentTooltipWord: string | null = null;
  private currentHighlightElement: HTMLElement | null = null;
  private isTooltipVisible = false;

  // Position cache to avoid redundant calculations
  private cachedPosition: { x: number; y: number } | null = null;
  private cachedElement: HTMLElement | null = null;

  // Mouse movement tracking for optimization
  private mouseMoveThrottle: number | null = null;
  private lastMouseCheck = { x: 0, y: 0 };
  private lastAreaCheckResult = false;
  private consecutiveInAreaCount = 0;
  private lastElementCounts: any = null;

  constructor(options: TooltipManagerOptions = {}) {
    this.options = {
      showDelay: TOOLTIP_CONFIG.SHOW_DELAY,
      hideDelay: TOOLTIP_CONFIG.HIDE_DELAY,
      graceMargin: TOOLTIP_CONFIG.GRACE_MARGIN,
      mouseThrottle: TOOLTIP_CONFIG.MOUSE_THROTTLE,
      theme: 'dark',
      ...options
    };
  }

  /**
   * 初始化 Tooltip 系统
   */
  initialize(): void {
    this.setupEventListeners();
    console.log('✅ [tooltip-manager|STARTUP] Tooltip manager initialized');
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 鼠标进入高亮单词
    document.addEventListener('mouseenter', this.handleMouseEnter.bind(this), true);
    
    // 鼠标移动监听
    document.addEventListener('mousemove', this.handleMouseMove.bind(this));
    
    // 点击事件处理
    document.addEventListener('click', this.handleClick.bind(this));
  }

  /**
   * 处理鼠标进入事件
   */
  private async handleMouseEnter(event: MouseEvent): Promise<void> {
    const target = event.target;

    if (target instanceof HTMLElement && target.classList.contains('lucid-highlight')) {
      const word = target.textContent?.trim() || 'unknown';
      
      console.log('🔧 [tooltip-manager|DEBUG] Mouse entered highlight:', word);

      // 取消隐藏定时器
      this.clearAllTimers();

      // 如果是同一个单词且tooltip已显示，不重复处理
      if (this.currentTooltipWord === word && this.isTooltipVisible) {
        console.log('🔧 [tooltip-manager|DEBUG] Same word tooltip already visible, skipping:', word);
        return;
      }

      // 如果有不同单词的tooltip正在显示，先隐藏它
      if (this.isTooltipVisible && this.currentTooltipWord !== word) {
        console.log('🔧 [tooltip-manager|DEBUG] Hiding previous tooltip for different word');
        await this.hideTooltip();
      }

      // 立即显示tooltip，不等待延迟
      this.showTooltip(target, word);
    }
  }

  /**
   * 处理鼠标移动事件
   */
  private handleMouseMove(event: MouseEvent): void {
    if (!this.isTooltipVisible || !this.currentHighlightElement) return;

    const mouseX = event.clientX;
    const mouseY = event.clientY;

    // Skip if mouse didn't move significantly (reduce redundant calculations)
    const deltaX = Math.abs(mouseX - this.lastMouseCheck.x);
    const deltaY = Math.abs(mouseY - this.lastMouseCheck.y);
    if (deltaX < 5 && deltaY < 5) return;

    // 节流处理，避免过于频繁的计算
    if (this.mouseMoveThrottle) return;

    this.mouseMoveThrottle = window.setTimeout(() => {
      this.lastMouseCheck = { x: mouseX, y: mouseY };

      // 检查鼠标是否仍在相关区域
      if (!this.isMouseInTooltipArea(mouseX, mouseY)) {
        // 鼠标离开相关区域，启动隐藏定时器
        if (!this.hideTimer) {
          console.log('🔧 [tooltip-manager|DEBUG] Mouse left tooltip area, starting hide timer');
          this.hideTimer = window.setTimeout(() => {
            this.hideTooltip();
          }, this.options.hideDelay);
        }
      } else {
        // 鼠标回到相关区域，取消隐藏定时器
        if (this.hideTimer) {
          console.log('🔧 [tooltip-manager|DEBUG] Mouse back in tooltip area, canceling hide timer');
          clearTimeout(this.hideTimer);
          this.hideTimer = null;
        }
      }

      this.mouseMoveThrottle = null;
    }, this.options.mouseThrottle);
  }

  /**
   * 处理点击事件
   */
  private handleClick(event: MouseEvent): void {
    if (!this.isTooltipVisible) return;

    const target = event.target as Element;
    if (!target) return;

    // 优化检测逻辑：优先检查tooltip内部
    let shouldKeepOpen = false;

    // 1. 优先检查：是否点击在tooltip内部（使用位置检测）
    const mouseX = event.clientX;
    const mouseY = event.clientY;
    const tooltipElement = this.getActualTooltipElement();

    if (tooltipElement) {
      const tooltipRect = tooltipElement.getBoundingClientRect();
      const inTooltipByPosition = mouseX >= tooltipRect.left && mouseX <= tooltipRect.right &&
        mouseY >= tooltipRect.top && mouseY <= tooltipRect.bottom;

      if (inTooltipByPosition) {
        shouldKeepOpen = true;
        console.log('🔧 [tooltip-manager|DEBUG] Click inside tooltip (position-based detection)', {
          mouseX, mouseY, tooltipRect
        });
      }
    }

    // 2. 备选检查：DOM元素检测
    if (!shouldKeepOpen) {
      // 检查是否点击在高亮单词上
      if (target.closest('.lucid-highlight')) {
        shouldKeepOpen = true;
        console.log('🔧 [tooltip-manager|DEBUG] Click on highlighted word');
      }

      // 检查是否点击在tooltip容器中（包括Shadow DOM）
      if (!shouldKeepOpen && (target.closest('lucid') || target.closest('lucid-ui'))) {
        shouldKeepOpen = true;
        console.log('🔧 [tooltip-manager|DEBUG] Click on tooltip container');
      }

      // 遍历Shadow DOM检查
      if (!shouldKeepOpen) {
        let current: Node | null = target;
        while (current) {
          if (current instanceof ShadowRoot) {
            const host = current.host;
            if (host && (host.tagName === 'LUCID' || host.tagName === 'LUCID-UI')) {
              shouldKeepOpen = true;
              console.log('🔧 [tooltip-manager|DEBUG] Click in Shadow DOM tooltip');
              break;
            }
          }
          current = current.parentNode;
        }
      }
    }

    if (!shouldKeepOpen) {
      this.clearAllTimers();
      this.hideTooltip();
      console.log('🔧 [tooltip-manager|DEBUG] Tooltip closed via outside click');
    } else {
      console.log('🔧 [tooltip-manager|DEBUG] Click detected inside tooltip area, keeping tooltip open', {
        targetClass: target.className,
        targetTag: target.tagName,
        position: { mouseX, mouseY },
        tooltipFound: !!tooltipElement
      });
    }
  }

  /**
   * 位置计算函数 - 确保tooltip在高亮单词正下方
   */
  private calculateTooltipPosition(rect: DOMRect): { x: number; y: number } {
    const gap = 6; // 减小间隔距离，更紧贴单词
    const tooltipHeight = 35; // 更准确的tooltip高度估算
    const tooltipWidth = 180; // 更准确的tooltip宽度估算

    // 获取页面滚动偏移
    const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
    const scrollY = window.pageYOffset || document.documentElement.scrollTop;

    // 计算相对于页面的绝对位置
    let x = rect.left + scrollX + rect.width / 2; // 水平居中对齐高亮单词
    let y = rect.bottom + scrollY + gap; // 紧贴在高亮单词下方

    // 边界检测 - 右边界
    if (x + tooltipWidth / 2 > window.innerWidth + scrollX) {
      x = window.innerWidth + scrollX - tooltipWidth / 2 - 10;
    }

    // 边界检测 - 左边界
    if (x - tooltipWidth / 2 < scrollX) {
      x = scrollX + tooltipWidth / 2 + 10;
    }

    // 边界检测 - 下边界，如果超出则显示在上方
    if (y + tooltipHeight > window.innerHeight + scrollY) {
      y = rect.top + scrollY - tooltipHeight - gap;
    }

    console.log('🔧 [tooltip-manager|DEBUG] Calculated tooltip position:', { x, y, rect, scrollX, scrollY });

    return { x, y };
  }

  /**
   * 获取缓存的或计算tooltip位置
   */
  private getCachedTooltipPosition(element: HTMLElement): { x: number; y: number } {
    // Return cached position if same element
    if (this.cachedElement === element && this.cachedPosition) {
      return this.cachedPosition;
    }

    // Calculate new position and cache it
    const rect = element.getBoundingClientRect();
    const position = this.calculateTooltipPosition(rect);

    this.cachedElement = element;
    this.cachedPosition = position;

    return position;
  }

  /**
   * 获取实际的tooltip DOM元素
   */
  private getActualTooltipElement(): Element | null {
    // 查找所有可能的tooltip容器
    const possibleHosts = [
      ...document.querySelectorAll('lucid'),     // ShadowView创建的元素
      ...document.querySelectorAll('lucid-ui'),  // 备选查找
      ...document.querySelectorAll('[class*="tooltip"]'), // CSS类名备选
    ];

    // 只在第一次或者元素数量变化时打印搜索信息
    const currentCounts = {
      lucidElements: document.querySelectorAll('lucid').length,
      lucidUiElements: document.querySelectorAll('lucid-ui').length,
      tooltipClassElements: document.querySelectorAll('[class*="tooltip"]').length
    };

    if (!this.lastElementCounts ||
      this.lastElementCounts.lucidElements !== currentCounts.lucidElements ||
      this.lastElementCounts.lucidUiElements !== currentCounts.lucidUiElements ||
      this.lastElementCounts.tooltipClassElements !== currentCounts.tooltipClassElements) {
      console.log('🔍 [tooltip-manager|TRACE] Searching for tooltip elements:', currentCounts);
      this.lastElementCounts = currentCounts;
    }

    for (const host of possibleHosts) {
      // 检查是否是tooltip类型的host
      if (host.shadowRoot) {
        const tooltipContent = host.shadowRoot.querySelector('.lu-tooltip');
        if (tooltipContent) {
          return host; // 返回host元素，因为它有位置信息
        }
      }

      // 直接检查元素本身是否包含tooltip
      if (host.querySelector) {
        const directTooltip = host.querySelector('.lu-tooltip');
        if (directTooltip) {
          return host;
        }
      }

      // 如果是tooltip相关的类名，也直接返回
      if (host.className && host.className.includes('tooltip')) {
        return host;
      }
    }

    // 最后的回退：如果找到任何 lucid 元素但无法验证内容，仍然返回它
    const lucidElements = document.querySelectorAll('lucid');
    if (lucidElements.length > 0) {
      console.log('🔍 [tooltip-manager|TRACE] Fallback: Using first lucid element without content verification');
      return lucidElements[0];
    }

    console.log('🔍 [tooltip-manager|TRACE] No tooltip element found');
    return null;
  }

  /**
   * 检查鼠标是否在tooltip相关区域
   */
  private isMouseInTooltipArea(mouseX: number, mouseY: number): boolean {
    // 1. 首先检查是否在实际的tooltip内部（最重要）
    const tooltipElement = this.getActualTooltipElement();
    if (tooltipElement) {
      const tooltipRect = tooltipElement.getBoundingClientRect();
      const tooltipArea = {
        left: tooltipRect.left - this.options.graceMargin,
        right: tooltipRect.right + this.options.graceMargin,
        top: tooltipRect.top - this.options.graceMargin,
        bottom: tooltipRect.bottom + this.options.graceMargin
      };

      const inTooltip = mouseX >= tooltipArea.left && mouseX <= tooltipArea.right &&
        mouseY >= tooltipArea.top && mouseY <= tooltipArea.bottom;

      // 如果在tooltip内，直接返回true，无需其他检查
      if (inTooltip) {
        if (!this.lastAreaCheckResult) {
          console.log('🔍 [tooltip-manager|TRACE] Mouse in tooltip area - keeping visible');
        }
        this.lastAreaCheckResult = inTooltip;
        return true;
      }
    }

    // 1.5. 备选方案：使用 elementFromPoint 直接检测鼠标下的元素
    const elementUnderMouse = document.elementFromPoint(mouseX, mouseY);
    if (elementUnderMouse) {
      // 检查是否在任何tooltip相关的元素内
      const inTooltipByElement =
        elementUnderMouse.closest('lucid') ||
        elementUnderMouse.closest('lucid-ui') ||
        elementUnderMouse.closest('.lu-tooltip') ||
        elementUnderMouse.classList.contains('lu-tooltip') ||
        elementUnderMouse.classList.contains('lu-chinese-short') ||
        elementUnderMouse.classList.contains('lu-pos');

      if (inTooltipByElement) {
        console.log('🔍 [tooltip-manager|TRACE] Mouse over tooltip element (elementFromPoint):', {
          element: elementUnderMouse.tagName,
          className: elementUnderMouse.className,
          mousePosition: { mouseX, mouseY }
        });
        return true;
      }
    }

    // 2. 检查高亮单词区域（作为备选）
    if (!this.currentHighlightElement) return false;

    const highlightRect = this.currentHighlightElement.getBoundingClientRect();
    const highlightArea = {
      left: highlightRect.left - this.options.graceMargin,
      right: highlightRect.right + this.options.graceMargin,
      top: highlightRect.top - this.options.graceMargin,
      bottom: highlightRect.bottom + this.options.graceMargin
    };

    const inHighlight = mouseX >= highlightArea.left && mouseX <= highlightArea.right &&
      mouseY >= highlightArea.top && mouseY <= highlightArea.bottom;

    // 3. 可选：连接区域检测（如果tooltip存在但鼠标不在其中）
    let inBridge = false;
    if (tooltipElement && !inHighlight) {
      const tooltipRect = tooltipElement.getBoundingClientRect();
      const bridgeArea = {
        left: Math.min(highlightArea.left, tooltipRect.left - this.options.graceMargin),
        right: Math.max(highlightArea.right, tooltipRect.right + this.options.graceMargin),
        top: Math.min(highlightArea.top, tooltipRect.top - this.options.graceMargin),
        bottom: Math.max(highlightArea.bottom, tooltipRect.bottom + this.options.graceMargin)
      };

      inBridge = mouseX >= bridgeArea.left && mouseX <= bridgeArea.right &&
        mouseY >= bridgeArea.top && mouseY <= bridgeArea.bottom;
    }

    const result = inHighlight || inBridge;

    // 记录状态变化
    if (result !== this.lastAreaCheckResult) {
      console.log('🔍 [tooltip-manager|TRACE] Mouse area check (state changed):', {
        mouseX, mouseY, inHighlight, inBridge, result,
        tooltipFound: !!tooltipElement
      });
      this.lastAreaCheckResult = result;
      this.consecutiveInAreaCount = 0;
    }

    return result;
  }

  /**
   * 显示tooltip
   */
  private async showTooltip(element: HTMLElement, word: string): Promise<void> {
    try {
      const position = this.getCachedTooltipPosition(element);

      console.log('✅ [tooltip-manager|INFO] Showing tooltip for word:', word, 'at position:', position);

      await uiManager.showTooltip({
        word,
        position,
        content: React.createElement(DynamicTooltip, {
          word,
          theme: this.options.theme,
          interactive: true  // 启用交互功能
        }),
        theme: this.options.theme
      });

      this.currentTooltipWord = word;
      this.currentHighlightElement = element;
      this.isTooltipVisible = true;
    } catch (error) {
      console.log('❌ [tooltip-manager|ERROR] Error showing tooltip:', error);
    }
  }

  /**
   * 隐藏tooltip
   */
  private async hideTooltip(): Promise<void> {
    try {
      console.log('✅ [tooltip-manager|INFO] Hiding tooltip');
      await uiManager.hideTooltips();
      this.currentTooltipWord = null;
      this.currentHighlightElement = null;
      this.isTooltipVisible = false;

      // Clear position cache when hiding tooltip
      this.cachedPosition = null;
      this.cachedElement = null;
      this.lastAreaCheckResult = false;
      this.consecutiveInAreaCount = 0;
    } catch (error) {
      console.log('❌ [tooltip-manager|ERROR] Error hiding tooltip:', error);
    }
  }

  /**
   * 清理所有定时器
   */
  private clearAllTimers(): void {
    if (this.showTimer) {
      clearTimeout(this.showTimer);
      this.showTimer = null;
    }
    if (this.hideTimer) {
      clearTimeout(this.hideTimer);
      this.hideTimer = null;
    }
    if (this.mouseMoveThrottle) {
      clearTimeout(this.mouseMoveThrottle);
      this.mouseMoveThrottle = null;
    }

    // Clear position cache when clearing timers
    this.cachedPosition = null;
    this.cachedElement = null;
    this.lastMouseCheck = { x: 0, y: 0 };
    this.lastAreaCheckResult = false;
    this.consecutiveInAreaCount = 0;
  }

  /**
   * 强制隐藏tooltip（用于ESC键等）
   */
  public async forceHide(): Promise<void> {
    this.clearAllTimers();
    await this.hideTooltip();
  }

  /**
   * 获取当前状态
   */
  public getState(): any {
    return {
      isTooltipVisible: this.isTooltipVisible,
      currentTooltipWord: this.currentTooltipWord,
      showTimer: this.showTimer !== null,
      hideTimer: this.hideTimer !== null
    };
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    try {
      this.clearAllTimers();
      
      // 清理状态
      this.currentTooltipWord = null;
      this.currentHighlightElement = null;
      this.isTooltipVisible = false;
      this.cachedPosition = null;
      this.cachedElement = null;
      this.lastElementCounts = null;
      
      // 强制隐藏任何显示的tooltip
      if (this.isTooltipVisible) {
        this.hideTooltip().catch(error => {
          errorLogger.warn('Failed to hide tooltip during destroy', error, {
            method: 'TooltipManager.destroy',
            component: 'TooltipManager'
          });
        });
      }

      errorLogger.info('TooltipManager destroyed successfully', {
        method: 'TooltipManager.destroy',
        component: 'TooltipManager'
      });
    } catch (error) {
      const context: ErrorContext = {
        method: 'TooltipManager.destroy',
        component: 'TooltipManager'
      };
      errorLogger.error('Failed to destroy TooltipManager', error as Error, context);
    }
  }
}

/**
 * 初始化 Tooltip 管理器的便捷函数
 */
export function initializeTooltips(options?: TooltipManagerOptions): TooltipManager {
  const manager = new TooltipManager(options);
  manager.initialize();
  return manager;
}