/**
 * 高亮系统与UI Manager集成
 * 演示如何在高亮系统中使用Shadow DOM管理器
 * 支持动态词典数据获取和静态数据显示
 */

// React import removed - not using JSX in this file
import { uiManager } from './uiManager';
// TODO: Fix import path after DynamicTooltip components are available
// import { DynamicTooltip, HighlightDynamicTooltip } from '@/components/DynamicTooltip';

/**
 * 为高亮的单词显示动态词典 Tooltip
 * @param word 高亮的单词
 * @param position 鼠标位置
 * @param options 可选配置
 */
export const showDynamicWordTooltip = async (
  word: string, 
  position: { x: number; y: number },
  options: {
    theme?: 'dark' | 'light';
    enableFreshData?: boolean;
    fallbackDefinition?: string;
  } = {}
) => {
  // 隐藏其他tooltip
  await uiManager.hideType('tooltip');

  const { theme = 'dark', enableFreshData = false, fallbackDefinition } = options;

  const tooltipId = await uiManager.showTooltip({
    word,
    position,
    // TODO: 使用动态组件获取真实词典数据
    // component: (
    //   <HighlightDynamicTooltip
    //     word={word}
    //     theme={theme}
    //     dictionaryOptions={{
    //       fresh: enableFreshData,
    //       timeout: 5000,
    //       retryCount: 1,
    //     }}
    //     fallbackContent={fallbackDefinition ? (
    //       <span className={`lu-tooltip lu-fallback`} data-theme={theme}>
    //         {fallbackDefinition}
    //       </span>
    //     ) : null}
    //   />
    // ),
    content: fallbackDefinition || `Loading ${word}...`, // 临时使用静态内容
    maxWidth: 300,
    delay: 50  // 更快的响应时间
  });

  return tooltipId;
};

/**
 * 为高亮的单词显示Tooltip（兼容性版本）
 * @param word 高亮的单词
 * @param position 鼠标位置
 * @param definition 单词定义
 * @deprecated 使用 showDynamicWordTooltip 获得更好的用户体验
 */
export const showWordTooltip = async (word: string, position: { x: number; y: number }, definition: string) => {
  return showDynamicWordTooltip(word, position, {
    fallbackDefinition: definition
  });
};

/**
 * 为高亮的单词显示详细信息窗口
 * @param word 高亮的单词
 * @param position 鼠标位置
 * @param wordData 详细单词数据
 */
export const showWordDetails = async (
  word: string,
  position: { x: number; y: number },
  wordData: {
    definitions: string[];
    examples: string[];
    pronunciation?: string;
    etymology?: string;
  }
) => {
  // 隐藏tooltip，显示详细信息
  await uiManager.hideType('tooltip');

  const toolfullId = await uiManager.showToolfull({
    word,
    position,
    data: wordData,
    maxWidth: 400,
    maxHeight: 500
  });

  return toolfullId;
};

/**
 * 在高亮元素上添加事件监听器
 * @param element 高亮元素
 * @param word 单词
 * @param wordData 单词数据
 */
export const attachHighlightEvents = (
  element: HTMLElement,
  word: string,
  wordData: {
    definitions: string[];
    examples: string[];
    pronunciation?: string;
    etymology?: string;
  }
) => {
  let tooltipId: string | null = null;
  let hoverTimer: number | null = null;

  // 鼠标进入 - 显示动态词典tooltip
  element.addEventListener('mouseenter', async (e) => {
    const rect = element.getBoundingClientRect();
    const position = {
      x: rect.left + rect.width / 2,
      y: rect.top - 10
    };

    // 延迟显示tooltip，使用动态词典数据
    hoverTimer = window.setTimeout(async () => {
      tooltipId = await showDynamicWordTooltip(word, position, {
        theme: 'dark',
        enableFreshData: false, // 使用缓存数据提高性能
        fallbackDefinition: wordData.definitions[0] // 作为备选显示
      });
    }, 200); // 减少延迟提升响应性
  });

  // 鼠标离开 - 隐藏tooltip
  element.addEventListener('mouseleave', async () => {
    if (hoverTimer) {
      clearTimeout(hoverTimer);
      hoverTimer = null;
    }

    if (tooltipId) {
      await uiManager.hide(tooltipId);
      tooltipId = null;
    }
  });

  // 点击 - 显示详细信息
  element.addEventListener('click', async (e) => {
    e.preventDefault();

    // 清理tooltip
    if (tooltipId) {
      await uiManager.hide(tooltipId);
      tooltipId = null;
    }

    if (hoverTimer) {
      clearTimeout(hoverTimer);
      hoverTimer = null;
    }

    const rect = element.getBoundingClientRect();
    const position = {
      x: rect.left + rect.width / 2,
      y: rect.bottom + 10
    };

    await showWordDetails(word, position, wordData);
  });

  // 右键 - 清除高亮
  element.addEventListener('contextmenu', async (e) => {
    e.preventDefault();

    // 隐藏所有相关UI
    await uiManager.hideAll();

    // 这里可以调用高亮系统的清除方法
    // 例如：highlighter.removeWordHighlight(word);

    console.log(`High light for "${word}" removed`);
  });
};

/**
 * 高亮系统的UI管理器助手类
 */
export class HighlightUIHelper {
  private static instance: HighlightUIHelper;

  static getInstance(): HighlightUIHelper {
    if (!this.instance) {
      this.instance = new HighlightUIHelper();
    }
    return this.instance;
  }

  /**
   * 为新创建的高亮元素设置UI交互
   * 使用动态词典数据，提供备选数据作为fallback
   */
  setupHighlightInteraction(element: HTMLElement, word: string, count: number) {
    // 提供备选数据，在动态获取失败时使用
    const fallbackWordData = {
      definitions: [
        `Definition for "${word}"`,
        `Alternative meaning of "${word}"`
      ],
      examples: [
        `Example sentence with "${word}".`,
        `Another example using "${word}".`
      ],
      pronunciation: `/${word}/`,
      etymology: `Etymology of "${word}"`
    };

    attachHighlightEvents(element, word, fallbackWordData);
  }

  /**
   * 为高亮元素设置动态词典交互（推荐方式）
   */
  setupDynamicHighlightInteraction(element: HTMLElement, word: string, options?: {
    theme?: 'dark' | 'light';
    enableFreshData?: boolean;
    customFallback?: string;
  }) {
    let tooltipId: string | null = null;
    let hoverTimer: number | null = null;

    const { theme = 'dark', enableFreshData = false, customFallback } = options || {};

    // 鼠标进入 - 显示动态tooltip
    element.addEventListener('mouseenter', async (e) => {
      const rect = element.getBoundingClientRect();
      const position = {
        x: rect.left + rect.width / 2,
        y: rect.top - 10
      };

      hoverTimer = window.setTimeout(async () => {
        tooltipId = await showDynamicWordTooltip(word, position, {
          theme,
          enableFreshData,
          fallbackDefinition: customFallback || `Loading definition for "${word}"...`
        });
      }, 150); // 快速响应
    });

    // 鼠标离开 - 隐藏tooltip
    element.addEventListener('mouseleave', async () => {
      if (hoverTimer) {
        clearTimeout(hoverTimer);
        hoverTimer = null;
      }

      if (tooltipId) {
        await uiManager.hide(tooltipId);
        tooltipId = null;
      }
    });

    // 点击 - 显示完整词典信息
    element.addEventListener('click', async (e) => {
      e.preventDefault();

      // 清理当前tooltip
      if (tooltipId) {
        await uiManager.hide(tooltipId);
        tooltipId = null;
      }

      if (hoverTimer) {
        clearTimeout(hoverTimer);
        hoverTimer = null;
      }

      const rect = element.getBoundingClientRect();
      const position = {
        x: rect.left + rect.width / 2,
        y: rect.bottom + 10
      };

      // 显示详细的动态词典信息
      await showDynamicWordTooltip(word, position, {
        theme,
        enableFreshData: true, // 点击时获取最新数据
        fallbackDefinition: customFallback
      });
    });

    // 右键 - 清除高亮
    element.addEventListener('contextmenu', async (e) => {
      e.preventDefault();
      await uiManager.hideAll();
      console.log(`Highlight for "${word}" removed`);
    });
  }

  /**
   * 清理所有UI
   */
  async clearAllUI() {
    await uiManager.hideAll();
  }

  /**
   * 获取UI统计信息
   */
  getUIStats() {
    return uiManager.getStats();
  }
}

// 全局导出助手实例
export const highlightUI = HighlightUIHelper.getInstance();

// 导出到全局作用域供开发使用
if (typeof window !== 'undefined') {
  (window as any).highlightUI = highlightUI;
  (window as any).showWordTooltip = showWordTooltip;
  (window as any).showDynamicWordTooltip = showDynamicWordTooltip;
  (window as any).showWordDetails = showWordDetails;
}