/**
 * DynamicTooltip 组件
 * 使用 useDictionary Hook 动态获取词典数据的 Tooltip 组件
 */

import React from "react";
import { Tooltip } from "@tooltip/Tooltip";
import {
  useDictionary,
  UseDictionaryOptions,
} from "@dictionary/useDictionary";
import { TooltipSkeleton } from "./TooltipSkeleton";
import { TooltipError } from "./TooltipError";

export interface DynamicTooltipProps {
  /** 要查询的单词 */
  word: string;
  /** 词典查询选项 */
  dictionaryOptions?: UseDictionaryOptions;
  /** 额外的类名 */
  className?: string;
  /** 主题，默认 dark */
  theme?: "dark" | "light";
  /** 是否显示加载状态 */
  showLoading?: boolean;
  /** 是否显示错误状态 */
  showError?: boolean;
  /** 空状态时的显示内容 */
  fallbackContent?: React.ReactNode;
  /** 加载状态的自定义组件 */
  LoadingComponent?: React.ComponentType<{ word: string }>;
  /** 错误状态的自定义组件 */
  ErrorComponent?: React.ComponentType<{
    error: Error;
    word: string;
    onRetry?: () => void;
  }>;
  /** 是否启用交互功能 */
  interactive?: boolean;
}

export const DynamicTooltip: React.FC<DynamicTooltipProps> = ({
  word,
  dictionaryOptions,
  className,
  theme = "dark",
  showLoading = true,
  showError = true,
  fallbackContent = null,
  LoadingComponent = TooltipSkeleton,
  ErrorComponent = TooltipError,
  interactive = false,
}) => {
  const { data, loading, error, isValidating, refetch } = useDictionary(
    word,
    dictionaryOptions
  );

  // 如果单词为空，不显示任何内容
  if (!word || !word.trim()) {
    return <>{fallbackContent}</>;
  }

  // 错误状态
  if (error && showError) {
    return <ErrorComponent error={error} word={word} onRetry={refetch} />;
  }

  // 加载状态
  if ((loading || isValidating) && showLoading) {
    return <LoadingComponent word={word} />;
  }

  // 有数据时显示完整的 Tooltip
  if (data) {
    return (
      <Tooltip
        word={data.word}
        phonetic={data.phonetic}
        explain={data.explain}
        className={className}
        theme={theme}
        interactive={interactive}
        onPreferenceUpdate={() => {
          // 偏好更新后不需要重新获取数据
          // Tooltip组件内部已经通过setVersion触发重新渲染和排序
          console.log('Preference updated, tooltip will re-render with new sorting');
        }}
      />
    );
  }

  // 没有数据时显示 fallback
  return <>{fallbackContent}</>;
};

/**
 * 带有预设配置的 DynamicTooltip 变体
 */
export const DynamicTooltipFresh: React.FC<
  Omit<DynamicTooltipProps, "dictionaryOptions">
> = (props) => {
  return <DynamicTooltip {...props} dictionaryOptions={{ fresh: true }} />;
};

/**
 * 无缓存的 DynamicTooltip 变体
 */
export const DynamicTooltipNoCache: React.FC<
  Omit<DynamicTooltipProps, "dictionaryOptions">
> = (props) => {
  return <DynamicTooltip {...props} dictionaryOptions={{ skipCache: true }} />;
};

/**
 * 静默的 DynamicTooltip 变体（不显示加载和错误状态）
 */
export const DynamicTooltipSilent: React.FC<DynamicTooltipProps> = (props) => {
  return <DynamicTooltip {...props} showLoading={false} showError={false} />;
};

/**
 * 创建带有默认配置的 DynamicTooltip 工厂函数
 */
export const createDynamicTooltip = (
  defaultOptions: Partial<DynamicTooltipProps> = {}
) => {
  return (props: DynamicTooltipProps) => {
    return <DynamicTooltip {...defaultOptions} {...props} />;
  };
};

/**
 * 用于高亮系统集成的 DynamicTooltip 配置 - 优化版本
 */
export const HighlightDynamicTooltip = createDynamicTooltip({
  showLoading: true,
  showError: false, // 在高亮场景中不显示错误状态
  dictionaryOptions: {
    timeout: 1500, // 快速失败，1.5秒超时
    retryCount: 0,  // 不重试，直接使用mock数据
    skipCache: false, // 使用缓存提升性能
  },
});

/**
 * 用于测试的 DynamicTooltip 配置
 */
export const TestDynamicTooltip = createDynamicTooltip({
  dictionaryOptions: {
    enabled: false, // 测试时默认禁用
  },
});

// 导出类型
export type { UseDictionaryOptions } from "@dictionary/useDictionary";
