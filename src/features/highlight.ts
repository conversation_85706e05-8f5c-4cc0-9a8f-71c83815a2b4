/**
 * 🎯 Lucid Highlight System - 简单的选择高亮系统
 * 
 * 设计原则：能跑、好改、文件少
 * 
 * 功能特性：
 * ✅ 手动选择高亮 (Shift + 选择)
 * ✅ 基本存储和样式管理
 * 
 * 使用示例：
 * ```typescript
 * const highlighter = new LucidHighlight();
 * await highlighter.initialize();
 * ```
 */

// === 类型定义 ===
interface HighlightConfig {
  effects?: string[];
  enabled?: boolean;
  minWordLength?: number;
  maxWordLength?: number;
  blacklistedWords?: string[];
}

// === 存储接口 ===
interface ExtensionStorage {
  get(key: string): Promise<any>;
  set(data: Record<string, any>): Promise<void>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
}

// === 高亮核心类 ===
export class LucidHighlight {
  private config: Required<HighlightConfig> = {
    effects: ["lu-gradient", "lu-underline"],
    enabled: true,
    minWordLength: 2,
    maxWordLength: 30,
    blacklistedWords: ["the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by"]
  };

  private wordCounts = new Map<string, number>();
  private storage: ExtensionStorage;
  private eventListeners: Array<{ element: EventTarget, event: string, handler: EventListener }> = [];
  private debounceTimer: number | null = null;

  constructor(config: HighlightConfig = {}) {
    this.config = { ...this.config, ...config };
    this.storage = null as any;
  }

  // === 存储系统 ===
  private async initStorageAsync(): Promise<ExtensionStorage> {
    console.log('⚙️ [highlight-storage|INIT] 存储系统初始化中...');

    const waitForExtensionAPI = (): Promise<any> => {
      return new Promise((resolve) => {
        let attempts = 0;
        const maxAttempts = 20;

        const checkAPI = () => {
          attempts++;
          const browserAPI = (globalThis as any).browser;
          const chromeAPI = (globalThis as any).chrome;
          const api = browserAPI?.storage?.local || chromeAPI?.storage?.local;

          if (api) {
            console.log('✅ [highlight-storage|API] 扩展存储API可用');
            resolve(api);
          } else if (attempts >= maxAttempts) {
            console.warn('⚠️ [highlight-storage|FALLBACK] 扩展存储API不可用，回退到localStorage');
            resolve(null);
          } else {
            setTimeout(checkAPI, 100);
          }
        };

        checkAPI();
      });
    };

    const api = await waitForExtensionAPI();

    if (!api) {
      return {
        get: async (key: string) => {
          try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : {};
          } catch (error) {
            console.error('localStorage get error:', error);
            return {};
          }
        },
        set: async (data: Record<string, any>) => {
          try {
            for (const [key, value] of Object.entries(data)) {
              localStorage.setItem(key, JSON.stringify(value));
            }
          } catch (error) {
            console.error('localStorage set error:', error);
          }
        },
        remove: async (key: string) => {
          try {
            localStorage.removeItem(key);
          } catch (error) {
            console.error('localStorage remove error:', error);
          }
        },
        clear: async () => {
          try {
            localStorage.clear();
          } catch (error) {
            console.error('localStorage clear error:', error);
          }
        }
      };
    }

    console.log('⚙️ [highlight-storage|MODE] 使用扩展存储API');
    return api;
  }

  private async loadWordCounts(): Promise<void> {
    try {
      if (!this.storage) {
        console.error('Storage not initialized');
        this.wordCounts = new Map();
        return;
      }

      const result = await this.storage.get("lucid-word-counts");
      let data = {};

      if (result && typeof result === 'object') {
        if (result["lucid-word-counts"]) {
          data = result["lucid-word-counts"];
        } else if (Object.keys(result).some(key => typeof result[key] === 'number')) {
          data = result;
        }
      }

      this.wordCounts = new Map(Object.entries(data));
      console.log(`✅ [highlight-storage|LOAD] 从存储加载${this.wordCounts.size}个词汇`);

      if (this.wordCounts.size === 0) {
        console.log('ℹ️ [highlight-storage|EMPTY] 未找到现有数据');
      }
    } catch (error) {
      console.error('Error loading word counts:', error);
      this.wordCounts = new Map();
    }
  }

  private async saveWordCounts(): Promise<void> {
    try {
      if (!this.storage) {
        console.warn('Storage not available, skipping save');
        return;
      }

      await this.storage.set({ "lucid-word-counts": Object.fromEntries(this.wordCounts) });
    } catch (error) {
      // Silently ignore extension context invalidation errors during development
      if (error instanceof Error && error.message.includes('Extension context invalidated')) {
        console.warn('🔄 Extension context invalidated, skipping word count save');
        return;
      }
      console.error('Error saving word counts:', error);
    }
  }

  // === 样式计算 ===
  private calculateLevel(count: number): number {
    if (count <= 0) return 1;
    return Math.min(Math.ceil(count / 2), 5);
  }

  private generateClasses(count: number, isDark: boolean): string[] {
    const level = this.calculateLevel(count);
    const classes = [`lu-level-${level}`, ...this.config.effects];
    if (isDark) classes.push("lu-dark-text");
    return classes;
  }

  private isDarkText(element: Element): boolean {
    const color = window.getComputedStyle(element).color;
    const rgbMatch = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
    if (!rgbMatch) return false;

    const [, r, g, b] = rgbMatch.map(Number);
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    return luminance < 0.5;
  }

  private containsMultipleWords(text: string): boolean {
    return /\s/.test(text) || /[.,;:!?]/.test(text);
  }

  // === DOM 操作 ===
  private createHighlightElement(text: string, count: number, isDark: boolean): HTMLElement {
    const element = document.createElement("lucid-highlight");
    element.textContent = text;
    element.dataset.word = text.toLowerCase();
    element.dataset.count = count.toString();

    const classes = this.generateClasses(count, isDark);
    element.classList.add('lucid-highlight', ...classes);

    return element;
  }

  private findExistingHighlights(word: string): HTMLElement[] {
    return Array.from(document.querySelectorAll("lucid-highlight"))
      .filter((el) => (el as HTMLElement).dataset.word === word) as HTMLElement[];
  }

  // === 事件处理 ===
  private async handleHighlight(): Promise<void> {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed) return;

    const range = selection.getRangeAt(0);
    const text = range.toString().trim();
    const word = text.toLowerCase();

    // 验证选择
    if (text.length < this.config.minWordLength) return;
    if (text.length > this.config.maxWordLength) return;
    if (this.containsMultipleWords(text)) return;
    if (this.config.blacklistedWords.includes(word)) return;

    try {
      // 增加计数
      const currentCount = this.wordCounts.get(word) || 0;
      this.wordCounts.set(word, currentCount + 1);

      // 保存到存储
      await this.saveWordCounts();

      // 高亮所有匹配的单词
      this.highlightAllOccurrences(word);

      // 清除选择
      selection.removeAllRanges();
    } catch (error) {
      console.error("Highlight error:", error);
    }
  }

  private createEventHandler(): EventListener {
    return (event: Event) => {
      if (!this.config.enabled) return;

      const keyEvent = event as KeyboardEvent;

      if (keyEvent.key === 'Shift' && !keyEvent.ctrlKey && !keyEvent.altKey && !keyEvent.metaKey) {
        if (this.debounceTimer) clearTimeout(this.debounceTimer);

        this.debounceTimer = window.setTimeout(() => {
          this.handleHighlight();
        }, 100);
      }
    };
  }

  private createContextMenuHandler(): EventListener {
    return async (event: Event) => {
      const mouseEvent = event as MouseEvent;
      const target = mouseEvent.target as HTMLElement;

      if (target.tagName === "LUCID-HIGHLIGHT" || target.tagName === "lucid-highlight" || target.classList.contains('lucid-highlight')) {
        mouseEvent.preventDefault();
        await this.removeHighlight(target);
      }
    };
  }

  // === 简单高亮系统 ===
  private highlightAllOccurrences(word: string): void {
    const count = this.wordCounts.get(word) || 1;
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_TEXT,
      null
    );

    const textNodes: Text[] = [];
    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node as Text);
    }

    const regex = new RegExp(`\\b${this.escapeRegExp(word)}\\b`, 'gi');

    textNodes.forEach(textNode => {
      const text = textNode.textContent || '';
      if (regex.test(text)) {
        this.highlightWordInTextNode(textNode, word, count);
      }
    });
  }

  private highlightWordInTextNode(textNode: Text, word: string, count: number): void {
    const parent = textNode.parentNode;
    if (!parent || this.isInExcludedElement(textNode)) return;

    const text = textNode.textContent || '';
    const regex = new RegExp(`\\b${this.escapeRegExp(word)}\\b`, 'gi');

    let lastIndex = 0;
    let match;
    const fragments: Node[] = [];

    while ((match = regex.exec(text)) !== null) {
      // Add text before match
      if (match.index > lastIndex) {
        fragments.push(document.createTextNode(text.substring(lastIndex, match.index)));
      }

      // Create highlight element
      const highlightElement = this.createHighlightElement(match[0], count, this.isDarkText(parent as Element));
      fragments.push(highlightElement);

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < text.length) {
      fragments.push(document.createTextNode(text.substring(lastIndex)));
    }

    // Replace the original text node with fragments
    if (fragments.length > 0) {
      fragments.forEach(fragment => parent.insertBefore(fragment, textNode));
      parent.removeChild(textNode);
    }
  }

  private isInExcludedElement(node: Node): boolean {
    let parent = node.parentElement;
    while (parent) {
      if (['SCRIPT', 'STYLE', 'NOSCRIPT', 'TEXTAREA', 'INPUT', 'SELECT', 'CODE', 'PRE'].includes(parent.tagName)) {
        return true;
      }
      // Check for both uppercase and lowercase (自定义元素通常是大写)
      if (parent.tagName === 'LUCID-HIGHLIGHT' || parent.tagName === 'lucid-highlight') {
        return true;
      }
      // Also check for class-based highlights
      if (parent.classList && parent.classList.contains('lucid-highlight')) {
        return true;
      }
      parent = parent.parentElement;
    }
    return false;
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  // === 公共接口 ===
  async initialize(): Promise<void> {
    try {
      // 移除了重复的存储系统初始化日志，因为在initStorageAsync中已经有了
      try {
        this.storage = await this.initStorageAsync();
      } catch (error) {
        console.error('Storage initialization failed, proceeding without persistence:', error);
        this.wordCounts = new Map();
      }

      await this.loadWordCounts();

      // 注册自定义元素
      if (typeof customElements !== 'undefined' && customElements && !customElements.get('lucid-highlight')) {
        customElements.define('lucid-highlight', class extends HTMLElement { });
      } else if (typeof customElements === 'undefined') {
        console.warn('customElements not available, skipping custom element registration');
      }

      // 设置事件处理
      const keyHandler = this.createEventHandler();
      const contextHandler = this.createContextMenuHandler();

      document.addEventListener("keydown", keyHandler);
      document.addEventListener("contextmenu", contextHandler);

      this.eventListeners.push(
        { element: document, event: "keydown", handler: keyHandler },
        { element: document, event: "contextmenu", handler: contextHandler }
      );

      console.log('✅ [highlight-system|STARTUP] Lucid高亮系统初始化完成');
    } catch (error) {
      console.error('Error during initialization:', error);
      throw error;
    }
  }

  destroy(): void {
    // 清理事件监听器
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
    this.eventListeners = [];

    // 清理定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    console.log("Lucid highlight system destroyed");
  }

  async removeHighlight(element: HTMLElement): Promise<void> {
    const word = element.dataset.word;
    if (!word) return;

    // 重置计数
    this.wordCounts.delete(word);

    // 移除元素
    const parent = element.parentNode;
    if (parent) {
      const textNode = document.createTextNode(element.textContent || "");
      parent.replaceChild(textNode, element);
    }

    // 保存更改
    await this.saveWordCounts();
  }

  async clearAll(): Promise<void> {
    this.wordCounts.clear();

    // 移除所有高亮元素
    const highlights = document.querySelectorAll("lucid-highlight");
    highlights.forEach(element => {
      const parent = element.parentNode;
      if (parent) {
        const textNode = document.createTextNode(element.textContent || "");
        parent.replaceChild(textNode, element);
      }
    });

    await this.saveWordCounts();
  }

  async getWordCount(word: string): Promise<number> {
    return this.wordCounts.get(word.toLowerCase()) || 0;
  }

  getHighlightedWords(): string[] {
    return Array.from(this.wordCounts.keys());
  }

  getHighlightStats(): { totalWords: number, totalHighlights: number } {
    const totalHighlights = document.querySelectorAll("lucid-highlight").length;
    return {
      totalWords: this.wordCounts.size,
      totalHighlights
    };
  }
}

// Global helper functions for console debugging
declare global {
  interface Window {
    lucidHighlighter: LucidHighlight | null;
  }
}

// Export global helper function
if (typeof window !== 'undefined') {
  window.clearLucidHighlights = async () => {
    if (window.lucidHighlighter) {
      await window.lucidHighlighter.clearAll();
      console.log('All Lucid highlights cleared!');
    } else {
      console.warn('Lucid highlighter not found. Make sure it\'s initialized.');
    }
  };
}