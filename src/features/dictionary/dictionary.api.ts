import { TooltipProps } from '@tooltip/Tooltip';
import {
  DictionaryApiResponse,
  DictionaryApiConfig,
  GetWordOptions,
  DictionaryApiError,
  PART_OF_SPEECH_MAP,
  SimpleChinese,
  ChineseTranslator,
  Phonetic
} from './types';

// Background script 响应类型
interface BackgroundResponse {
  success: boolean;
  data?: any;
  error?: string;
}

// API 原始响应类型
interface RawApiResponse {
  success: boolean;
  data: {
    words: Array<{
      word: string;
      phonetic?: Phonetic;
      explain?: Array<{
        pos: string;
        definitions: Array<{
          definition: string;
          chinese: string;
          chinese_short: string;
        }>;
      }>;
    }>;
  };
}

/**
 * 词典API服务
 * 负责与词典API交互，获取和转换词典数据
 */
export class DictionaryApi {
  private readonly config: Required<DictionaryApiConfig>;
  private readonly translator: ChineseTranslator;

  // Simple logger with prefix
  private log = (level: 'info' | 'warn' | 'error', message: string, ...args: any[]) => {
    const emoji = { info: '📡', warn: '⚠️', error: '❌' };
    console[level](`${emoji[level]} [DictAPI] ${message}`, ...args);
  };

  constructor(config: DictionaryApiConfig = {}) {
    this.config = {
      baseUrl: config.baseUrl ?? 'http://localhost:3003',
      timeout: config.timeout ?? 2000, // 减少超时时间到2秒，快速失败
      retryCount: config.retryCount ?? 0, // 禁用重试，快速回退到 mock 数据
      retryDelay: config.retryDelay ?? 500 // 减少重试延迟
    };

    this.translator = new SimpleChinese();
  }

  /**
   * 获取单词数据
   * @param word 单词
   * @param options 选项
   * @returns 转换后的词典数据
   */
  async getWord(word: string, options: GetWordOptions = {}): Promise<TooltipProps | null> {
    if (!word || typeof word !== 'string') {
      throw new DictionaryApiError('Invalid word parameter', 'INVALID_PARAM');
    }

    const cleanWord = word.trim().toLowerCase();
    if (!cleanWord) {
      throw new DictionaryApiError('Empty word parameter', 'INVALID_PARAM');
    }

    try {
      const apiResponse = await this.fetchFromApi(cleanWord, options);
      if (!apiResponse) {
        return null;
      }

      // Check if it's already in TooltipProps format (from real API)
      if ('explain' in apiResponse) {
        return apiResponse as TooltipProps;
      }

      // Otherwise transform DictionaryApiResponse to TooltipProps
      return await this.transformToTooltipProps(apiResponse as DictionaryApiResponse);
    } catch (error) {
      console.warn(`API unavailable for "${word}", using mock data:`, error);

      // Fallback to mock data when API is unavailable
      return this.getMockData ? this.getMockData(cleanWord) : null;
    }
  }

  /**
   * 从API获取原始数据
   * @param word 单词
   * @param options 选项
   * @returns API原始响应
   */
  private async fetchFromApi(word: string, options: GetWordOptions): Promise<TooltipProps | null> {
    const timeout = options.timeout ?? this.config.timeout;

    for (let attempt = 0; attempt <= this.config.retryCount; attempt++) {
      try {
        // 优先使用background script代理（解决CORS问题）
        const data = await this.fetchWithProxy(word, timeout);
        return this.validateApiResponse(data) as TooltipProps;
      } catch (error) {
        if (error instanceof DictionaryApiError) {
          throw error;
        }

        if (attempt < this.config.retryCount) {
          await this.delay(this.config.retryDelay);
          continue;
        }

        // 最后一次尝试失败
        if (error instanceof Error && error.name === 'AbortError') {
          throw new DictionaryApiError('Request timeout', 'TIMEOUT');
        }

        throw new DictionaryApiError(
          `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
          'NETWORK_ERROR'
        );
      }
    }

    return null;
  }

  /**
   * 构建API URL
   * @param word 单词
   * @param options 选项
   * @returns API URL
   */
  private buildApiUrl(word: string, options: GetWordOptions): string {
    const url = new URL(`/api/dictionary/en/${encodeURIComponent(word)}`, this.config.baseUrl);

    if (options.fresh) {
      url.searchParams.set('fresh', 'true');
    }

    return url.toString();
  }

  /**
   * 使用background script代理获取数据（解决CORS问题）
   * @param word 单词
   * @param timeout 超时时间
   * @returns API响应数据
   */
  private async fetchWithProxy(word: string, timeout: number): Promise<RawApiResponse | DictionaryApiResponse> {
    try {
      this.log('info', 'Using background script proxy for:', word);

      // 添加快速失败机制：如果background script在短时间内没有响应，立即失败
      const response: BackgroundResponse = await Promise.race([
        browser.runtime.sendMessage({
          type: 'DICTIONARY_API_REQUEST',
          payload: { word, options: { timeout: Math.min(timeout, 1500) } } // 限制最大1.5秒
        }),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('Background proxy timeout')), 1500)
        )
      ]);

      this.log('info', 'Background response received for:', word);

      // Handle undefined response (background script not responding)
      if (!response) {
        throw new Error('Background script not responding (undefined response)');
      }

      if (response.success) {
        this.log('info', 'Background proxy successful for:', word);
        return response.data;
      } else {
        throw new Error(response.error || 'Background proxy request failed');
      }
    } catch (error) {
      // Handle extension context invalidation gracefully
      if (error instanceof Error && error.message.includes('Extension context invalidated')) {
        this.log('warn', 'Extension context invalidated, using mock data');
        throw new Error('Extension context invalidated - using fallback data');
      }
      
      this.log('warn', 'Background proxy failed, using mock data:', error instanceof Error ? error.message : 'Unknown error');
      throw new Error(`Background proxy unavailable: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 带超时的fetch请求
   * @param url URL
   * @param timeout 超时时间
   * @returns fetch响应
   */
  private async fetchWithTimeout(url: string, timeout: number): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    try {
      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * 验证API响应格式
   * @param data API响应数据
   * @returns 验证后的数据
   */
  private validateApiResponse(data: RawApiResponse | DictionaryApiResponse): DictionaryApiResponse | TooltipProps {
    console.log('🔍 Validating API response:', JSON.stringify(data, null, 2));

    // Handle the real API format: {success, data: {words: [...]}}
    if ('success' in data && data.success && data.data && Array.isArray(data.data.words)) {
      console.log('✅ Detected real API format, extracting word data...');

      if (data.data.words.length === 0) {
        throw new DictionaryApiError('No words found in API response', 'INVALID_RESPONSE');
      }

      const wordData = data.data.words[0];
      console.log('📝 Extracted word data:', wordData);

      // Transform to expected TooltipProps format
      const transformedData = {
        word: wordData.word,
        phonetic: wordData.phonetic,
        explain: wordData.explain?.map((exp: any) => ({
          pos: exp.pos?.replace('.', '') || 'unknown',
          definitions: exp.definitions?.map((def: any) => ({
            definition: def.definition || '',
            chinese: def.chinese || '',
            chinese_short: def.chinese_short || ''
          })) || []
        })) || [],
        theme: 'dark' as const
      };

      console.log('🔄 Transformed data:', transformedData);
      console.log('📋 First definition check:', transformedData.explain[0]?.definitions[0]);
      return transformedData as TooltipProps;
    }

    // Handle legacy/mock format: {word, meanings, phonetic}
    if (!data || typeof data !== 'object') {
      throw new DictionaryApiError('Invalid API response format', 'INVALID_RESPONSE');
    }

    if (!(data as any).word || typeof (data as any).word !== 'string') {
      console.error('❌ Missing or invalid word field. Available fields:', Object.keys(data));
      throw new DictionaryApiError('Missing word in API response', 'INVALID_RESPONSE');
    }

    if (!Array.isArray((data as any).meanings) || (data as any).meanings.length === 0) {
      throw new DictionaryApiError('Missing meanings in API response', 'INVALID_RESPONSE');
    }

    return data as DictionaryApiResponse;
  }

  /**
   * 转换API响应为TooltipProps格式
   * @param apiResponse API响应
   * @returns TooltipProps格式的数据
   */
  private async transformToTooltipProps(apiResponse: DictionaryApiResponse): Promise<TooltipProps> {
    // 提取音标信息
    const phonetic = this.extractPhonetic(apiResponse);

    // 转换释义信息
    const explain = await this.transformMeanings(apiResponse.meanings);

    return {
      word: apiResponse.word,
      phonetic,
      explain,
      theme: 'dark'
    };
  }

  /**
   * 提取音标信息
   * @param apiResponse API响应
   * @returns 音标信息
   */
  private extractPhonetic(apiResponse: DictionaryApiResponse): { us?: string; uk?: string } {
    const phonetic = {
      us: '',
      uk: ''
    };

    // 从主要音标字段提取
    if (apiResponse.phonetic) {
      phonetic.us = apiResponse.phonetic;
      phonetic.uk = apiResponse.phonetic;
    }

    // 从phonetics数组提取更详细的音标信息
    if (apiResponse.phonetics && apiResponse.phonetics.length > 0) {
      for (const phoneticItem of apiResponse.phonetics) {
        if (phoneticItem.text) {
          // 优先使用第一个有效音标
          if (!phonetic.us) {
            phonetic.us = phoneticItem.text;
          }
          if (!phonetic.uk) {
            phonetic.uk = phoneticItem.text;
          }
        }
      }
    }

    return phonetic;
  }

  /**
   * 转换释义信息
   * @param meanings API释义数组
   * @returns 转换后的释义
   */
  private async transformMeanings(meanings: DictionaryApiResponse['meanings']): Promise<TooltipProps['explain']> {
    const explain: TooltipProps['explain'] = [];

    for (const meaning of meanings) {
      const pos = PART_OF_SPEECH_MAP[meaning.partOfSpeech] || meaning.partOfSpeech;
      const definitions = [];

      for (const definition of meaning.definitions) {
        try {
          const translation = await this.translator.translate(definition.definition, pos);
          definitions.push({
            definition: definition.definition,
            chinese: translation.chinese,
            chinese_short: translation.chinese_short
          });
        } catch (error) {
          console.warn('Translation failed for definition:', definition.definition, error);
          // 使用fallback翻译
          definitions.push({
            definition: definition.definition,
            chinese: definition.definition,
            chinese_short: pos
          });
        }
      }

      if (definitions.length > 0) {
        explain.push({
          pos,
          definitions
        });
      }
    }

    return explain;
  }

  /**
   * 延迟函数
   * @param ms 毫秒数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取API配置
   * @returns 当前配置
   */
  getConfig(): DictionaryApiConfig {
    return { ...this.config };
  }

  /**
   * 获取模拟数据（当API不可用时使用）
   * @param word 单词
   * @returns 模拟的词典数据
   */
  getMockData(word: string): TooltipProps | null {
    const mockData: Record<string, TooltipProps> = {
      hello: {
        word: 'hello',
        phonetic: { us: '/həˈloʊ/', uk: '/həˈləʊ/' },
        explain: [
          {
            pos: 'noun',
            definitions: [{
              definition: 'A greeting or expression of goodwill.',
              chinese: '问候或善意的表达',
              chinese_short: '问候'
            }]
          },
          {
            pos: 'verb',
            definitions: [{
              definition: 'To greet someone by saying hello.',
              chinese: '通过说你好来问候某人',
              chinese_short: '问候'
            }]
          }
        ]
      },
      translating: {
        word: 'translating',
        phonetic: { us: '/trænsˈleɪtɪŋ/', uk: '/trænsˈleɪtɪŋ/' },
        explain: [
          {
            pos: 'verb',
            definitions: [{
              definition: 'Converting text or speech from one language to another.',
              chinese: '将文本或语音从一种语言转换为另一种语言',
              chinese_short: '翻译'
            }]
          }
        ]
      },
      example: {
        word: 'example',
        phonetic: { us: '/ɪɡˈzæmpəl/', uk: '/ɪɡˈzɑːmpəl/' },
        explain: [
          {
            pos: 'noun',
            definitions: [{
              definition: 'A thing characteristic of its kind or illustrating a general rule.',
              chinese: '例子，实例，范例',
              chinese_short: '例子'
            }]
          }
        ]
      },
      extensions: {
        word: 'extensions',
        phonetic: { us: '/ɪkˈstenʃənz/', uk: '/ɪkˈstenʃənz/' },
        explain: [
          {
            pos: 'noun',
            definitions: [{
              definition: 'A part that is added to something to make it larger or longer.',
              chinese: '扩展，延伸，附加部分',
              chinese_short: '扩展'
            }]
          }
        ]
      }
    };

    const result = mockData[word];
    if (result) {
      console.log(`📚 Using mock data for word: ${word}`);
      return result;
    }

    // 生成通用的模拟数据
    console.log(`📚 Generating generic mock data for: ${word}`);
    return {
      word,
      phonetic: { us: '/.../', uk: '/.../' },
      explain: [
        {
          pos: 'noun',
          definitions: [{
            definition: `A definition for "${word}".`,
            chinese: `"${word}"的定义`,
            chinese_short: '定义'
          }]
        }
      ]
    };
  }

  /**
   * 测试API连接
   * @returns 是否连接成功
   */
  async testConnection(): Promise<boolean> {
    try {
      const response = await browser.runtime.sendMessage({
        type: 'HEALTH_CHECK'
      });
      return response.success;
    } catch {
      // Fallback to direct connection test
      try {
        const response = await this.fetchWithTimeout(
          new URL('/api/health', this.config.baseUrl).toString(),
          5000
        );
        return response.ok;
      } catch {
        return false;
      }
    }
  }
}

// 导出默认实例
export const dictionaryApi = new DictionaryApi();